using System.Collections;
using Il2Cpp;
using MelonLoader;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace TestLE.Patches;

public static class MonolithPulsePatches
{
    [AutoPatch]
    public static void InitializePostfix(MinimapFogOfWar __instance, MinimapFogOfWar.QuadScale quadScale, Vector3 vector3, Vector3 test)
    {
        // HandleFogOfWar(__instance);
    }
}



// using HarmonyLib;
// using Il2CppLE.Gameplay.Monolith.Frontend;
// using MelonLoader;
//
// namespace TestLE.Patches;
//
// [HarmonyPatch(typeof(MonolithPulse), "ReceiveShow")]
// public class Patch_MonolithPulse_ReceiveShow
// {
//     public static void Postfix(MonolithPulse __instance)
//     {
//         MelonLogger.Msg("MonolithPulse ReceiveShow");
//
//         if (__instance.completed)
//         {
//             MelonLogger.Msg("MonolithPulse already completed");
//             return;
//         }
//
//         MelonLogger.Msg("MonolithPulse initialized");
//         _ = new MonolithObjective(__instance);
//     }
// }
//
// [HarmonyPatch(typeof(MonolithPulse), "ReceiveHide")]
// public class Patch_MonolithPulse_ReceiveHide
// {
//     public static void Postfix(MonolithPulse __instance)
//     {
//         MelonLogger.Msg("MonolithPulse ReceiveHide");
//
//         if (__instance.completed)
//         {
//             MelonLogger.Msg("MonolithPulse already completed");
//             return;
//         }
//
//         MelonLogger.Msg("MonolithPulse initialized");
//         _ = new MonolithObjective(__instance);
//     }
// }
//
// [HarmonyPatch(typeof(MonolithPulse), "OnDestroy")]
// public class Patch_MonolithPulse_OnDestroy
// {
//     public static void Postfix(MonolithPulse __instance)
//     {
//         MONOLITH_OBJECTIVES.RemoveAll(x => x == null || x.MonolithPulse == __instance);
//     }
// }
