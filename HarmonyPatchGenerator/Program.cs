// Harmony Patch Generator CLI with TUI mode
// - Generates Harmony boilerplate patch classes for MelonLoader mods.
// - Now supports an interactive TUI (using Spectre.Console) to browse types and methods.
//
// Build:
//   dotnet build -c Release
// Run examples:
//   genpatch -a "./Assembly-CSharp.dll" --tui -o ./Generated
//
// TUI Flow:
//   1. Loads assembly.
//   2. Lets you select a type interactively.
//   3. Lets you multi-select methods.
//   4. Generates patch files for chosen methods.

using System;
using System.IO;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using dnlib.DotNet;
using Spectre.Console;

namespace HarmonyPatchGenerator
{
    internal static class Program
    {
        private static int Main(string[] args)
        {
            int exitCode = 0;
            try
            {
                var opts = Options.Parse(args);
                if (opts == null)
                {
                    Options.PrintHelp();
                    exitCode = 1;
                }
                else if (!File.Exists(opts.AssemblyPath))
                {
                    Console.Error.WriteLine($"[ERR] Assembly not found: {opts.AssemblyPath}");
                    exitCode = 2;
                }
                else
                {
                    Directory.CreateDirectory(opts.OutputDir);
                    var mod = ModuleDefMD.Load(opts.AssemblyPath);

                    if (opts.UseTui)
                    {
                        RunTui(mod, opts);
                        exitCode = 0;
                    }
                    else
                    {
                        exitCode = RunBatch(mod, opts);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine("[FATAL] " + ex);
                exitCode = 100;
            }
            finally
            {
                WaitForUserInput();
            }

            return exitCode;
        }

        private static int RunBatch(ModuleDefMD mod, Options opts)
        {
            var typeMatches = GetTypeMatches(mod, opts);
            if (!typeMatches.Any())
            {
                Console.Error.WriteLine("[WARN] No matching types found.");
                return 3;
            }

            int generated = 0;
            foreach (var t in typeMatches)
            {
                var methodMatches = GetMethodMatches(t, opts);
                foreach (var m in methodMatches)
                {
                    WritePatchFile(t, m, opts, ref generated);
                }
            }

            if (generated == 0)
            {
                Console.Error.WriteLine("[WARN] No methods matched your filters.");
                return 4;
            }

            Console.WriteLine($"[DONE] Generated {generated} patch file(s) to {opts.OutputDir}");
            return 0;
        }

        private static void RunTui(ModuleDefMD mod, Options opts)
        {
            var allTypes = mod.Types.Where(t => !t.IsGlobalModuleType).ToList();

            // Step 1: choose a type
            var chosenTypeName = AnsiConsole.Prompt(
                new SelectionPrompt<string>()
                    .Title("Select a [green]type[/]:")
                    .PageSize(15)
                    .MoreChoicesText("(Move up and down to reveal more types)")
                    .AddChoices(allTypes.Select(t => t.FullName)));

            var chosenType = allTypes.First(t => t.FullName == chosenTypeName);

            // Step 2: choose methods
            var methodChoices = chosenType.Methods
                .Where(m => !m.IsConstructor && !m.IsStaticConstructor)
                .Select(m => new
                {
                    Method = m,
                    Display = $"{m.Name}({string.Join(", ", m.Parameters.Where(p => p.IsNormalMethodParameter).Select(p => p.Type.TypeName))})"
                })
                .ToList();

            var chosenMethods = AnsiConsole.Prompt(
                new MultiSelectionPrompt<string>()
                    .Title($"Select [green]methods[/] from {chosenTypeName}:")
                    .NotRequired()
                    .PageSize(15)
                    .InstructionsText("[grey](Press <space> to toggle, <enter> to confirm)[/]")
                    .AddChoices(methodChoices.Select(mc => mc.Display)));

            int generated = 0;
            foreach (var display in chosenMethods)
            {
                var mc = methodChoices.First(m => m.Display == display);
                WritePatchFile(chosenType, mc.Method, opts, ref generated);
            }

            AnsiConsole.MarkupLine($"[green]Generated {generated} patch file(s) to {opts.OutputDir}[/]");
        }

        private static void WritePatchFile(TypeDef t, MethodDef m, Options opts, ref int counter)
        {
            var code = RenderPatchClass(t, m, opts.Namespace, opts.IncludePostfix, opts.IncludePrefix);
            var safeTypeName = SafeIdent(t.FullName);
            var safeMethodName = SafeIdent(m.Name);
            var fileName = Path.Combine(opts.OutputDir, $"Patch_{safeTypeName}_{safeMethodName}_{counter + 1}.cs");
            File.WriteAllText(fileName, code, new UTF8Encoding(false));
            Console.WriteLine($"[OK] Generated: {fileName}");
            counter++;
        }

        // ... (RenderPatchClass, SafeIdent, DescribeParam, GetTypeMatches, GetMethodMatches unchanged from previous version) ...

        private static string RenderPatchClass(TypeDef t, MethodDef m, string @namespace, bool includePostfix, bool includePrefix)
        {
            var sb = new StringBuilder();

            sb.AppendLine("using HarmonyLib;");
            sb.AppendLine("using MelonLoader;");
            sb.AppendLine();
            sb.AppendLine($"namespace {@namespace};");
            sb.AppendLine();
            sb.AppendLine($"[HarmonyPatch(typeof({t.FullName}))]");
            sb.AppendLine($"public class Patch_{SafeIdent(t.FullName)}_{SafeIdent(m.Name)}");
            sb.AppendLine("{");

            if (includePrefix)
            {
                sb.AppendLine("    [HarmonyPrefix]");
                sb.AppendLine($"    [HarmonyPatch(\"{m.Name}\")]");
                sb.AppendLine($"    private static void {m.Name}_Prefix({DescribeParams(m)})");
                sb.AppendLine("    {");
                sb.AppendLine($"        MelonLogger.Msg(\"{t.Name}.{m.Name} Prefix\");");
                sb.AppendLine("    }");

                if (includePostfix)
                    sb.AppendLine();
            }

            if (includePostfix)
            {
                sb.AppendLine("    [HarmonyPostfix]");
                sb.AppendLine($"    [HarmonyPatch(\"{m.Name}\")]");
                sb.AppendLine($"    private static void {m.Name}_Postfix({DescribeParams(m)})");
                sb.AppendLine("    {");
                sb.AppendLine($"        MelonLogger.Msg(\"{t.Name}.{m.Name} Postfix\");");
                sb.AppendLine("    }");
            }

            sb.AppendLine("}");

            return sb.ToString();
        }

        private static string DescribeParams(MethodDef m)
        {
            var parameters = new List<string>();

            if (!m.IsStatic)
                parameters.Add($"{m.DeclaringType.FullName} __instance");

            foreach (var param in m.Parameters.Where(p => p.IsNormalMethodParameter))
            {
                parameters.Add($"{param.Type.FullName} {param.Name}");
            }

            return string.Join(", ", parameters);
        }

        private static string SafeIdent(string s)
        {
            var sb = new StringBuilder();
            foreach (var ch in s)
            {
                if (char.IsLetterOrDigit(ch)) sb.Append(ch);
                else sb.Append('_');
            }
            return sb.ToString();
        }

        private static void WaitForUserInput()
        {
            try
            {
                Console.WriteLine();
                Console.WriteLine("Press any key to continue...");
                Console.ReadKey(true);
            }
            catch
            {
                // If we can't read from console, just wait a bit
                System.Threading.Thread.Sleep(2000);
            }
        }

        private static IEnumerable<TypeDef> GetTypeMatches(ModuleDefMD mod, Options opts)
        {
            IEnumerable<TypeDef> allTypes = mod.Types;
            allTypes = allTypes.Where(t => t.FullName != null && !t.IsGlobalModuleType);

            if (string.IsNullOrEmpty(opts.TypeFilter))
                return allTypes;

            if (opts.UseRegex)
                return allTypes.Where(t => System.Text.RegularExpressions.Regex.IsMatch(t.FullName, opts.TypeFilter));

            return allTypes.Where(t => t.FullName.EndsWith(opts.TypeFilter) || t.Name == opts.TypeFilter || t.FullName == opts.TypeFilter);
        }

        private static IEnumerable<MethodDef> GetMethodMatches(TypeDef t, Options opts)
        {
            var methods = t.Methods.Where(m => !m.IsConstructor && !m.IsStaticConstructor);

            if (opts.AllMethods)
                return methods;

            if (string.IsNullOrEmpty(opts.MethodFilter) || opts.MethodFilter == ".*")
                return methods;

            if (opts.UseRegex)
                return methods.Where(m => System.Text.RegularExpressions.Regex.IsMatch(m.Name, opts.MethodFilter));

            return methods.Where(m => m.Name == opts.MethodFilter);
        }
    }

    internal sealed class Options
    {
        public string AssemblyPath = string.Empty;
        public string OutputDir = "./Generated";
        public string TypeFilter = string.Empty;
        public string MethodFilter = string.Empty;
        public bool UseRegex = false;
        public bool AllMethods = false;
        public string Namespace = "GeneratedPatches";
        public bool IncludePrefix = true;
        public bool IncludePostfix = false;
        public bool UseTui = false;

        public static Options? Parse(string[] args)
        {
            if (args.Length == 0) return null;
            var o = new Options();
            for (int i = 0; i < args.Length; i++)
            {
                string a = args[i];
                switch (a)
                {
                    case "-a": case "--assembly": o.AssemblyPath = NextArg(args, ref i); break;
                    case "-o": case "--out": o.OutputDir = NextArg(args, ref i); break;
                    case "-t": case "--type": o.TypeFilter = NextArg(args, ref i); break;
                    case "-m": case "--method": o.MethodFilter = NextArg(args, ref i); break;
                    case "--regex": o.UseRegex = true; break;
                    case "--all": o.AllMethods = true; break;
                    case "-n": case "--namespace": o.Namespace = NextArg(args, ref i); break;
                    case "--no-prefix": o.IncludePrefix = false; break;
                    case "--postfix": o.IncludePostfix = true; break;
                    case "--tui": o.UseTui = true; break;
                    case "-h": case "--help": return null;
                    default:
                        Console.Error.WriteLine($"Unknown arg: {a}");
                        return null;
                }
            }

            if (string.IsNullOrWhiteSpace(o.AssemblyPath)) return null;
            return o;
        }

        public static void PrintHelp()
        {
            Console.WriteLine(@"Harmony Patch Generator with TUI
Usage:
  genpatch -a <assembly.dll> [-t <typeFilter>] [-m <methodFilter>] [-o <outDir>] [--regex] [--all] [-n <namespace>] [--no-prefix] [--postfix] [--tui]

Examples:
  genpatch -a ./Assembly-CSharp.dll --tui -o ./Generated
  genpatch -a ./Assembly-CSharp.dll -t GameManager -m StartGame -o ./Generated

Notes:
  - With --tui, interactive type & method selection is enabled.
  - Without --tui, filters work as in batch mode.
  - Prefix is generated by default; add --postfix to include a Postfix; use --no-prefix to omit Prefix.
");
        }

        private static string NextArg(string[] args, ref int i)
        {
            if (i + 1 >= args.Length) throw new ArgumentException($"Missing value for {args[i]}");
            return args[++i];
        }
    }
}

// ---------- csproj ----------
/*
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyName>genpatch</AssemblyName>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="dnlib" Version="4.0.0" />
    <PackageReference Include="HarmonyLib" Version="2.3.3" />
    <PackageReference Include="MelonLoader" Version="0.6.1" />
    <PackageReference Include="Spectre.Console" Version="0.49.1" />
  </ItemGroup>
</Project>
*/
